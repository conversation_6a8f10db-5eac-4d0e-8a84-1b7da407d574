import {
	post,
	requestBody,
	api,
	get,
	getModelSchemaRef,
	patch,
	response,
	param,
	HttpErrors
} from '@loopback/rest';

import {guardStrategy, GuardSkipStrategy, skipGuardCheck, injectUserOrgId} from '../interceptors/crud-guard.interceptor';
import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {basicAuthorization, ShopperInfo} from '../services';
import {Integration, Organization, OrganizationIntegrationDetails} from '../models';
import {repository} from '@loopback/repository';
import {inject, service} from '@loopback/core';
import {IntegrationRepository, OrganizationIntegrationDetailsRepository, OrganizationKeysRepository, OrganizationSegmentRepository, RaleonUserIdentityRepository, OrganizationRepository} from '../repositories';
import crypto from 'crypto';
import { OrganizationOrganizationKeysController, DEFAULT_SECRET_KEY_NAME } from './organization-organization-keys.controller';
import {SkioIntegration} from '../services/integrations/skio.service';
import {StayIntegration} from '../services/integrations/stay.service';
import {PriveIntegration} from '../services/integrations/prive.service';
import {LoopIntegration} from '../services/integrations/loop.service';
import {OrganizationSegmentController} from './organization-segment.controller';
import {ShopifyApiInvoker} from '../services/shopify/shopify-api-invoker.service';
import {PromptCacheService} from '../services/prompt/prompt-cache.service';

const INTEGRATION_DEV_API_URL = 'https://son3tvhjo2.execute-api.us-east-1.amazonaws.com/v1/integrations';
//const INTEGRATION_ADAM_API_URL = 'https://gnlppj3qp0.execute-api.us-east-1.amazonaws.com/v1/integrations';
const INTEGRATION_API_URL = process.env.INTEGRATION_API_URL || INTEGRATION_DEV_API_URL;
const WEBAPP_URL = process.env.RALEON_WEBAPP_URL || 'https://dev.raleon.io';
const ENCRYPTION_KEY = 'mymommatoldmeineedtogotochurches'; // Must be 256 bits (32 characters)
const IV_LENGTH = 16; // For AES, this is always 16

const fetch = require('node-fetch')

const JUDGE_ME_INTEGRATION_ID = 1;
const KLAVIYO_INTEGRATION_ID = 2;
const STAMPED_INTEGRATION_ID = 3;
const SENDLANE_SYNC_TOKEN = 'Going0nH31p3dAnnouncementH0urTh3n';
const LOOP_INTEGRATION_ID = 8;
const SHOPIFY_INTEGRATION_ID = 9;
const LOOP_API_KEY = 'Loop-API-Key';

@api({basePath: '/api/v1'})
@guardStrategy(new GuardSkipStrategy())

export class IntegrationsController {
	@service(PromptCacheService)
	private promptCacheService: PromptCacheService;
	constructor(
		@repository(IntegrationRepository) protected integrationRepository: IntegrationRepository,
		@repository(OrganizationIntegrationDetailsRepository) protected organizationIntegrationDetailsRepository: OrganizationIntegrationDetailsRepository,
		@repository(OrganizationKeysRepository) protected organizationKeysRepository: OrganizationKeysRepository,
		@inject('controllers.OrganizationOrganizationKeysController') private organizationKeysController: OrganizationOrganizationKeysController,
		@service(SkioIntegration) private skioIntegrationService: SkioIntegration,
		@service(StayIntegration) private stayIntegrationService: StayIntegration,
		@service(PriveIntegration) private priveIntegrationService: PriveIntegration,
		@service(LoopIntegration) private loopIntegrationService: LoopIntegration,
		@inject('controllers.OrganizationSegmentController') private orgSegmentController: OrganizationSegmentController,
		@repository(OrganizationSegmentRepository) protected organizationSegmentRepository: OrganizationSegmentRepository,
		@repository(RaleonUserIdentityRepository) protected raleonUserIdentityRepository: RaleonUserIdentityRepository,
		@service(ShopperInfo)public shopperInfo: ShopperInfo,
		@service(ShopifyApiInvoker) private shopifyApiInvoker: ShopifyApiInvoker,
		@repository(OrganizationRepository) private organizationRepository: OrganizationRepository,
        ) { }

	//Returns whether this org has an integration connected and enabled of a particular type
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@get('/integrations/{category}', {
		responses: {
			'200': {
				description: 'whether the integration is enabled in this org for this category',
				content: {
					'application/json': {
						schema: {
						},
					},
				},
			},
		},
	})
	async isIntegrationEnabled(
		@injectUserOrgId() orgId: number,
		@param.path.string('category') category: string
	): Promise<any> {
		let integrations = await this.integrationRepository.find(
		{
			where: {
				category: category,
			},
			include: [{
				relation: 'organizationIntegrationDetails',
				scope: {
					where: {
						orgId: orgId,
						enabled: true
					}
				}
			}]
		});

		return {
			enabled: integrations.length > 0
		};
	}

	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@patch('/integration/{id}')
	@response(204, {
		description: 'Updated Organization Integration Detail',
	})
	async updateById(
		@param.path.number('id')
		id: number,
		@injectUserOrgId() orgId: number,
		@requestBody({
			content: {
				'application/json': {
					schema: getModelSchemaRef(OrganizationIntegrationDetailsRepository, {partial: true}),
				},
			},
		})
		integration: OrganizationIntegrationDetails,
	): Promise<void> {
		//verify that the integration belongs to the org
		const integrationDetails = await this.organizationIntegrationDetailsRepository.findOne({
			where: {
				id: id,
				orgId: orgId
			}
		});

		if (!integrationDetails) {
			throw new Error('Integration not found');
		}

		await this.organizationIntegrationDetailsRepository.updateById(id, integration);
	}

	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@get('/integrations', {
		responses: {
			'200': {
				description: 'Array of Integration model instances',
				content: {
					'application/json': {
						schema: {
							type: 'array',
							items: getModelSchemaRef(Integration, {
								includeRelations: true,
							}),
						},
					},
				},
			},
		}})
	async find(
		@injectUserOrgId() orgId: number,
	): Promise<any[]> {
		let integrations = await this.integrationRepository.find({
			order: ['sortOrder ASC']
		});
		let orgIntegrations = await this.organizationIntegrationDetailsRepository.find({
			where: {
				orgId: orgId
			}
		});

		let finalResponse = [];
		for (let integration of integrations) {
			let orgIntegration = orgIntegrations.find((orgIntegration) => {
				return orgIntegration.integrationId == integration.id;
			});

			// Check for matching keys based on integration ID
			let hasMatchingKey = false;
			if (integration.id === KLAVIYO_INTEGRATION_ID) {
				const klaviyoKey = await this.organizationKeysRepository.findOne({
					where: {
						organizationId: orgId,
						key: {eq: KLAVIYO_INTEGRATION_KEY},
						secretKeyId: DEFAULT_SECRET_KEY_NAME
					}
				});
				hasMatchingKey = !!klaviyoKey;
			} else if (integration.id === LOOP_INTEGRATION_ID) {
				const loopKey = await this.organizationKeysRepository.findOne({
					where: {
						organizationId: orgId,
						key: {eq: LOOP_API_KEY},
						secretKeyId: DEFAULT_SECRET_KEY_NAME
					}
				});
				hasMatchingKey = !!loopKey;
			} else if (integration.id === SHOPIFY_INTEGRATION_ID) {
				 const shopifyConnected = await this.isShopifyConnected(orgId);
				 hasMatchingKey = shopifyConnected.connected;
			}

			if (orgIntegration) {
				const {accessToken, orgId, ...orgIntegrationWithoutToken} = orgIntegration;
				finalResponse.push({
					...integration,
					...orgIntegrationWithoutToken,
					connected: accessToken != null || hasMatchingKey
				});
			} else {
				finalResponse.push({
					...integration,
					...{
						orgId: orgId,
						active: false,
						connected: hasMatchingKey
					}
				});
			}
		}
		return finalResponse;
	}

	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@post('/integration/judgeme/exchange', {
		responses: {
			'200': {
				description: 'Exchanges tokens for a Judgeme access token',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async exchangeJudgeMeToken(
		@requestBody({
			content: {
				'application/json': {
					schema: {},
				},
			},
		}) body: any,
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		try {
			const response = await fetch(`https://judge.me/oauth/token`, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					client_id: `${process.env.JUDGE_ME_CLIENT_ID}`,
					client_secret: `${process.env.JUDGE_ME_SECRET_ID}`,
					code: body.code,
					grant_type: 'authorization_code',
					redirect_uri: `${WEBAPP_URL}/integrations?name=judgeme`
				}),
			});
			const json = await response.json();
			if(json.error) {
				return {
					error: json.error,
					success: false
				};
			}
			console.log(json);
			//Format
			/*
				{
					access_token: '...',
					token_type: 'Bearer',
					scope: 'read_reviews read_reviewers read_orders read_products',
					created_at: 1704213204
				}

			*/
			//Update or create the integration org details
			const integration = await this.organizationIntegrationDetailsRepository.findOne({
				where: {
					orgId: orgId,
					integrationId: JUDGE_ME_INTEGRATION_ID
				}
			});

			if (integration) {
				integration.accessToken = encrypt(json.access_token);
				await this.organizationIntegrationDetailsRepository.update(integration);
			} else {
				await this.organizationIntegrationDetailsRepository.create({
					orgId: orgId,
					integrationId: JUDGE_ME_INTEGRATION_ID,
					accessToken: encrypt(json.access_token),
					connectedDate: new Date().toUTCString()
				});
			}

			return {
				success: true
			};
		}
		catch (e) {
			console.log(e);
			return {
				error: e,
				success: false
			};
		}
	}

	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@post('/integration/judgeme/enable')
	async connectJudgeMe(
		@requestBody({
			content: {
				'application/json': {
					schema: {},
				},
			},
		}) body: any,
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		//Verify body includes enabled
		if(!('enabled' in body)) {
			throw new Error('Enabled must be included in body');
		}

		//verify that the integration belongs to the org
		const integrationDetails = await this.organizationIntegrationDetailsRepository.findOne({
			where: {
				integrationId: JUDGE_ME_INTEGRATION_ID,
				orgId: orgId
			}
		});

		if (!integrationDetails) {
			throw new Error('Integration not found');
		}

		let access_token = decrypt(integrationDetails.accessToken!);

		//If enabled register webhook
		if(body.enabled) {
			let response = await fetch(`https://judge.me/api/v1/webhooks?name=judgeme`, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					'Authorization': `Bearer ${access_token}`
				},
				body: JSON.stringify({
					key: "review/created",
					url: `${INTEGRATION_API_URL}?name=judgeme`,
				}),
			});
			const json = await response.json();

			await this.organizationIntegrationDetailsRepository.updateById(integrationDetails.id, {
				enabled: response.status == 201
			});

			return {
				success: response.status == 201,
				data: json
			};
		}
		else {
			let response = await fetch(`https://judge.me/api/v1/webhooks?name=judgeme`, {
				method: 'DELETE',
				headers: {
					'Content-Type': 'application/json',
					'Authorization': `Bearer ${access_token}`
				},
				body: JSON.stringify({
					key: "review/created",
					url: `${INTEGRATION_API_URL}?name=judgeme`,
				}),
			});
			const json = await response.json();

			await this.organizationIntegrationDetailsRepository.updateById(integrationDetails.id, {
				enabled: false
			});

			return {
				success: response.status == 200,
				data: json
			};
		}
	}


	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@post('/integration/stamped/enable')
	async connectStamped(
		@requestBody({
			content: {
				'application/json': {
					schema: {},
				},
			},
		}) body: any,
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		//Verify body includes enabled
		if(!('enabled' in body)) {
			throw new Error('Enabled must be included in body');
		}

		//verify that the integration belongs to the org
		let integrationDetails = await this.organizationIntegrationDetailsRepository.findOne({
			where: {
				integrationId: STAMPED_INTEGRATION_ID,
				orgId: orgId
			}
		});

		if (!integrationDetails) {
			integrationDetails = await this.organizationIntegrationDetailsRepository.create({
				orgId: orgId,
				integrationId: STAMPED_INTEGRATION_ID,
				accessToken: '',
				connectedDate: new Date().toUTCString()
			});
		}

		await this.organizationIntegrationDetailsRepository.updateById(integrationDetails.id, {
			enabled: body.enabled
		});

		return {
			success: true,
		};
	}

	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@post('/integration/klaviyo/connect')
	async enableKlaviyo(
		@requestBody({
			content: {
				'application/json': {
					schema: {},
				},
			},
		}) body: any,
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		if (!body.apiKey) {
			throw new Error('API key must be included in body');
		}

		const orgIntegrationKey = await this.organizationKeysRepository.findOne({
			where: {
				organizationId: orgId,
				key: {eq: KLAVIYO_INTEGRATION_KEY},
				secretKeyId: DEFAULT_SECRET_KEY_NAME
			}
		})

		await this.validateKlaviyoApiKey(body.apiKey);

		// Check for existing integration details with scope-failure
		const existingDetails = await this.organizationIntegrationDetailsRepository.findOne({
			where: {
				orgId: orgId,
				integrationId: KLAVIYO_INTEGRATION_ID,
				connectedDate: 'scope-failure'
			}
		});

		if (existingDetails) {
			// Clear the connectedDate if it was previously set to scope-failure
			await this.organizationIntegrationDetailsRepository.updateById(existingDetails.id, {
				connectedDate: ''
			});
		}

		// if a key exists and the new key is valid, delete the existing key and create a new one
		if (orgIntegrationKey) {
			await this.organizationKeysRepository.delete(orgIntegrationKey);
		}

		const generatedKey = await this.organizationKeysController.create({
			key: KLAVIYO_INTEGRATION_KEY,
			value: body.apiKey,
			secretKeyId: DEFAULT_SECRET_KEY_NAME
		}, orgId);

		// Clear the prompt cache when Klaviyo is connected
		try {
			await this.promptCacheService.clearCache(orgId);
			console.log(`Cleared prompt cache for org ${orgId} after Klaviyo connection`);
		} catch (error) {
			console.error(`Error clearing prompt cache for org ${orgId}:`, error);
			// Don't fail the entire operation if cache clearing fails
		}

		return {
			success: true,
		};
	}

	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@post('/integration/klaviyo/disconnect')
	async disconnectKlaviyo(
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		const orgIntegrationKey = await this.organizationKeysRepository.findOne({
			where: {
				organizationId: orgId,
				key: {eq: KLAVIYO_INTEGRATION_KEY},
				secretKeyId: DEFAULT_SECRET_KEY_NAME
			}
		})

		if (orgIntegrationKey) {
			await this.organizationKeysRepository.delete(orgIntegrationKey);
		}

		// Clear the prompt cache when Klaviyo is disconnected
		try {
			await this.promptCacheService.clearCache(orgId);
			console.log(`Cleared prompt cache for org ${orgId} after Klaviyo disconnection`);
		} catch (error) {
			console.error(`Error clearing prompt cache for org ${orgId}:`, error);
			// Don't fail the entire operation if cache clearing fails
		}

		return {
			success: true,
		};
	}

	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@get('/integration/klaviyo/connected')
	async isKlaviyoConnected(
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		const orgIntegrationKey = await this.organizationKeysRepository.findOne({
			where: {
				organizationId: orgId,
				key: {eq: KLAVIYO_INTEGRATION_KEY},
				secretKeyId: DEFAULT_SECRET_KEY_NAME
			}
		})

		return {
			success: true,
			connected: !!orgIntegrationKey
		};
	}

	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@get('/integration/shopify/connected')
	async isShopifyConnected(
			@injectUserOrgId() orgId: number,
	): Promise<any> {
		try {
			// Try to fetch shop information - this will only work if the app is installed
			// and has proper access
			const response = await this.shopifyApiInvoker.invokeAdminApi(
				orgId,
				'/get-shop-info',  // This endpoint gets basic shop information
				'GET'
			);

			if (response) {
					const org = await this.organizationRepository.findById(orgId);
					if (!org.shopifyConnectedDate) {
							await this.organizationRepository.updateById(orgId, {
									shopifyConnectedDate: new Date().toISOString(),
							});
					}
			}

			// If we get here without an error, the app is installed
			return {
					success: true,
					connected: !!response
			};
		} catch (error) {
			console.log('App installation check failed:', error);
			return {
				success: true,
				connected: false
			}
		}
	}



	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@post('/integration/sendlane/connect')
	async enableSendlane(
		@requestBody({
			content: {
				'application/json': {
					schema: {},
				},
			},
		}) body: any,
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		if (!body.apiKey) {
			throw new Error('API key must be included in body');
		}

		const orgIntegrationKey = await this.organizationKeysRepository.findOne({
			where: {
				organizationId: orgId,
				key: {eq: SENDLANE_INTEGRATION_KEY},
				secretKeyId: DEFAULT_SECRET_KEY_NAME
			}
		})

		const orgCustomIntegrationKey = await this.organizationKeysRepository.findOne({
			where: {
				organizationId: orgId,
				key: {eq: SENDLANE_CUSTOM_INTEGRATION_KEY},
				secretKeyId: DEFAULT_SECRET_KEY_NAME
			}
		})


		const lists = await this.getSendlaneLists(body.apiKey);
		const raleonList = lists.find((list: any) => list.name === 'Raleon');
		if (!raleonList) {
			await this.createSendlaneContactList(body.apiKey);
		}

		const customIntegration = await this.createSendlaneCustomIntegration(body.apiKey);

		// if a key exists and the new key is valid, delete the existing key and create a new one
		if (orgIntegrationKey) {
			await this.organizationKeysRepository.delete(orgIntegrationKey);
		}
		if (orgCustomIntegrationKey) {
			await this.organizationKeysRepository.delete(orgCustomIntegrationKey);
		}

		const generatedKey = await this.organizationKeysController.create({
			key: SENDLANE_INTEGRATION_KEY,
			value: body.apiKey,
			secretKeyId: DEFAULT_SECRET_KEY_NAME
		}, orgId);

		const generatedToken = await this.organizationKeysController.create({
			key: SENDLANE_CUSTOM_INTEGRATION_KEY,
			value: customIntegration.token,
			secretKeyId: DEFAULT_SECRET_KEY_NAME
		}, orgId);

		// const testContact = await this.getOrFindSendlaneContact(body.apiKey, '<EMAIL>', {
		// 	test: 'test',
		// 	test2: 'test2 value'
		// });
		// const testContact2 = await this.getOrFindSendlaneContact(body.apiKey, '<EMAIL>', {
		// 	test: 'test',
		// 	test2: 'test2 value 2'
		// });

		// this.sendSendlaneTestEvent(testContact, body.apiKey, customIntegration.token);

		// test calling API >240 times in parallel
		// const promises = [];
		// for (let i = 0; i < 250; i++) {
		// 	promises.push(this.getSendlaneContactByEmail(body.apiKey, `<EMAIL>${i}`));
		// }
		// await Promise.all(promises);

		//await this.syncSendlane(orgId);

		// const contacts = await this.getSendlaneContacts(body.apiKey, customIntegration.token);
		// console.log(contacts);

		return {
			success: true,
		};
	}

	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@post('/integration/sendlane/disconnect')
	async disconnectSendlane(
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		const orgIntegrationKey = await this.organizationKeysRepository.findOne({
			where: {
				organizationId: orgId,
				key: {eq: SENDLANE_INTEGRATION_KEY},
				secretKeyId: DEFAULT_SECRET_KEY_NAME
			}
		})

		if (orgIntegrationKey) {
			const orgCustomIntegrationKey = await this.organizationKeysRepository.findOne({
				where: {
					organizationId: orgId,
					key: {eq: SENDLANE_CUSTOM_INTEGRATION_KEY},
					secretKeyId: DEFAULT_SECRET_KEY_NAME
				}
			})

			// const decryptedKey = decrypt2(orgIntegrationKey.value, ENCRYPTION_KEY);
			const decryptedKey = await this.organizationKeysController.findKey('Sendlane-API-Key', orgId);
			// try {
			// 	const lists = await this.getSendlaneLists(decryptedKey[0]?.value);
			// 	const raleonList = lists.find((list: any) => list.name === 'Raleon');
			// 	if (raleonList) {
			// 		await this.deleteSendlaneContactList(decryptedKey[0]?.value, raleonList.id);
			// 	}
			// } catch (e) {
			// 	console.error(e);
			// }

			try {
				if (orgCustomIntegrationKey) {

					const decryptedToken = await this.organizationKeysController.findKey('Sendlane-Custom-Integration-Token', orgId);
					if (!decryptedKey?.length || !decryptedToken?.length) {
						throw new Error('Sendlane API key / token not found');
					}
					await this.deleteExistingSendlaneCustomIntegration(decryptedKey[0]?.value, decryptedToken[0]?.value);

					await this.organizationKeysRepository.delete(orgCustomIntegrationKey);
				}
			} catch (e) {
				console.error(e);
			}

			await this.organizationKeysRepository.delete(orgIntegrationKey);
		}

		return {
			success: true,
		};
	}

	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@get('/integration/sendlane/connected')
	async isSendlaneConnected(
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		const orgIntegrationKey = await this.organizationKeysRepository.findOne({
			where: {
				organizationId: orgId,
				key: {eq: SENDLANE_INTEGRATION_KEY},
				secretKeyId: DEFAULT_SECRET_KEY_NAME
			}
		})

		return {
			success: true,
			connected: !!orgIntegrationKey
		};
	}

	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@post('/integration/disconnect', {
		responses: {
			'200': {
				description: 'Removes the integration from the organization',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async disconnectIntegration(
		@requestBody({
			content: {
				'application/json': {
					schema: {},
				},
			},
		}) body: any,
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		try {
			const integration = await this.organizationIntegrationDetailsRepository.findOne({
				where: {
					orgId: orgId,
					id: body.integrationId
				}
			});
			if (integration) {
				await this.organizationIntegrationDetailsRepository.deleteById(integration.id);
			}
			return {
				success: true
			};
		}
		catch (e) {
			console.log(e);
			return {
				error: e,
				success: false
			};
		}
	}

	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@post('/integration/skio/connect')
	async connectSkio(
		@requestBody({
			content: {
				'application/json': {
					schema: {},
				},
			},
		}) body: any,
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		let response;
		try {
			const enabledResponse = await this.skioIntegrationService.enableSkio(orgId);
			response = await this.skioIntegrationService.saveSkioKeys(orgId, body);
		} catch (e) {
			console.log(e);
			await this.skioIntegrationService.disableSkio(orgId);
			return {
				error: e,
				success: false
			};
		}

		return response;
	}

	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@post('/integration/skio/disconnect')
	async disconnectSkio(
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		await this.skioIntegrationService.disableSkio(orgId);
		return await this.skioIntegrationService.deleteSkioKeys(orgId);
	}


	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@get('/integration/skio/connected')
	async isSkioConnected(
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		const connected = await this.skioIntegrationService.isSkioConnected(orgId);
		return {
			success: true,
			connected,
		}
	}

	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@post('/integration/stay/connect')
	async connectStay(
		@requestBody({
			content: {
				'application/json': {
					schema: {},
				},
			},
		}) body: any,
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		let response;
		try {
			const enabledResponse = await this.stayIntegrationService.enableStay(orgId);
			response = await this.stayIntegrationService.saveStayKeys(orgId, body);
		} catch (e) {
			console.log(e);
			await this.stayIntegrationService.disableStay(orgId);
			return {
				error: e,
				success: false
			};
		}

		if (response && response.success) {
			const webhookResponse = await this.stayIntegrationService.createWebhookListener(orgId, body, INTEGRATION_API_URL);
			if (webhookResponse && webhookResponse.status < 300) {
				return {
					success: true,
				};
			} else {
				await this.stayIntegrationService.disableStay(orgId);
				throw new HttpErrors.InternalServerError('Failed to create webhook listener');
			}
		}

		return response;
	}

	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@post('/integration/stay/disconnect')
	async disconnectStay(
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		await this.stayIntegrationService.disableStay(orgId);
		const stayApiKey = await this.organizationKeysController.findKey('Stay-API-Key', orgId);
		if (stayApiKey?.[0]) {
			await this.stayIntegrationService.deleteWebhookListeners(orgId, { apiKey: stayApiKey[0].value });
		}
		return await this.stayIntegrationService.deleteStayKeys(orgId);
	}


	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@get('/integration/stay/connected')
	async isStayConnected(
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		const connected = await this.stayIntegrationService.isStayConnected(orgId);
		return {
			success: true,
			connected,
		}
	}

	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@post('/integration/loop/connect')
	async connectLoop(
		@requestBody({
			content: {
				'application/json': {
					schema: {},
				},
			},
		}) body: any,
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		let response;
		try {
			const enabledResponse = await this.loopIntegrationService.enableLoop(orgId);
			response = await this.loopIntegrationService.saveLoopKeys(orgId, body);
		} catch (e) {
			console.log(e);
			await this.loopIntegrationService.disableLoop(orgId);
			return {
				error: e,
				success: false
			};
		}

		if (response && response.success) {
			const webhookResponse = await this.loopIntegrationService.createWebhookListener(orgId, body, INTEGRATION_API_URL);
			if (webhookResponse && webhookResponse.status === 200) {
				return {
					success: true,
				};
			} else {
				await this.loopIntegrationService.disableLoop(orgId);
				throw new HttpErrors.InternalServerError('Failed to create webhook listener');
			}
		}

		return response;
	}

	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@post('/integration/loop/disconnect')
	async disconnectLoop(
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		await this.loopIntegrationService.disableLoop(orgId);
		const loopApiKey = await this.organizationKeysController.findKey(LOOP_API_KEY, orgId);
		if (loopApiKey?.[0]) {
			await this.loopIntegrationService.deleteWebhookListeners(orgId, { apiKey: loopApiKey[0].value });
		}
		return await this.loopIntegrationService.deleteLoopKeys(orgId);
	}

	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@get('/integration/loop/connected')
	async isLoopConnected(
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		const connected = await this.loopIntegrationService.isLoopConnected(orgId);
		return {
			success: true,
			connected,
		}
	}

	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@post('/integration/prive/connect')
	async connectPrive(
		@requestBody({
			content: {
				'application/json': {
					schema: {},
				},
			},
		}) body: any,
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		let response;
		try {
			await this.priveIntegrationService.enablePrive(orgId);
			response = await this.priveIntegrationService.savePriveKeys(orgId, body);
		} catch (e) {
			console.log(e);
			await this.priveIntegrationService.disablePrive(orgId);
			return {
				error: e,
				success: false
			};
		}

		if (response && response.success) {
			const webhookResponse = await this.priveIntegrationService.createWebhookListener(orgId, body, INTEGRATION_API_URL);
			if (webhookResponse && webhookResponse.status < 300) {
				return {
					success: true,
				};
			} else {
				await this.priveIntegrationService.disablePrive(orgId);
				throw new HttpErrors.InternalServerError('Failed to create webhook listener');
			}
		}

		return response;
	}

	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@post('/integration/prive/disconnect')
	async disconnectPrive(
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		await this.priveIntegrationService.disablePrive(orgId);
		const stayApiKey = await this.organizationKeysController.findKey('Stay-API-Key', orgId);
		if (stayApiKey?.[0]) {
			await this.priveIntegrationService.deleteWebhookListeners(orgId, { apiKey: stayApiKey[0].value });
		}
		return await this.priveIntegrationService.deletePriveKeys(orgId);
	}


	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@get('/integration/prive/connected')
	async isPriveConnected(
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		const connected = await this.priveIntegrationService.isPriveConnected(orgId);
		return {
			success: true,
			connected,
		}
	}

	private async validateKlaviyoApiKey(key: string) {
		if (!key) {
			throw new Error('Invalid Klaviyo API key');
		}

		const baseUrl = 'https://a.klaviyo.com/api';
		const endpoints = [
			{ path: '/accounts', method: 'GET', scope: 'Accounts Read' },
			{ path: '/metrics', method: 'GET', scope: 'Metrics Read' },
			{ path: '/campaigns?filter=equals(messages.channel,\'email\')', method: 'GET', scope: 'Campaigns Read' },
			{ path: '/events', method: 'GET', scope: 'Events Read' },
			{ path: '/events', method: 'POST', scope: 'Events Write', body: { data: { type: 'event', attributes: { metric: { name: 'Test Event' } } } } },
			{ path: '/lists', method: 'GET', scope: 'Lists Read' },
			{ path: '/lists', method: 'POST', scope: 'Lists Write', body: { data: { type: 'list', attributes: { name: 'Test List' } } } },
			{ path: '/profiles', method: 'GET', scope: 'Profiles Read' },
			{ path: '/profiles', method: 'POST', scope: 'Profiles Write', body: { data: { type: 'profile', attributes: { email: '<EMAIL>' } } } },
			{ path: '/segments', method: 'GET', scope: 'Segments Read' },
			{ path: '/segments', method: 'POST', scope: 'Segments Write', body: { data: { type: 'segment', attributes: { name: 'Test Segment' } } } },
			{ path: '/flows', method: 'GET', scope: 'Flows Read' },
			{ path: '/flows', method: 'POST', scope: 'Flows Write', body: { data: { type: 'flow', attributes: { name: 'Test Flow', status: 'draft' } } } }
		];

		const missingScopes: string[] = [];

		for (const endpoint of endpoints) {
			const options: any = {
				method: endpoint.method,
				headers: {
					accept: 'application/json',
					'content-type': 'application/json',
					revision: '2024-02-15',
					Authorization: `Klaviyo-API-Key ${key}`
				}
			};

			if (endpoint.body) {
				options.body = JSON.stringify(endpoint.body);
			}

			try {
				const response = await fetch(baseUrl + endpoint.path, options);
				// For POST requests, 403 means missing write scope, while 404/422 might be valid responses
				if (endpoint.method === 'POST' && response.status === 403) {
					missingScopes.push(endpoint.scope);
				}
				// For GET requests, anything not 200 means missing read scope
				else if (endpoint.method === 'GET' && response.status !== 200) {
					missingScopes.push(endpoint.scope);
				}
			} catch (error) {
				missingScopes.push(endpoint.scope);
			}
		}

		if (missingScopes.length > 0) {
			throw new HttpErrors.Unauthorized(
				`Klaviyo API key is missing required scopes: ${missingScopes.join(', ')}. Please ensure your API key has all required permissions.`
			);
		}

		return true;
	}



	@post('/sync-sendlane-data')
	@skipGuardCheck()
	async syncSendlaneForAllOrgs(
		@requestBody({
			content: {
				'application/json': {
					schema: {},
				},
			},
		}) body: any,
	) {
		if (body.token != SENDLANE_SYNC_TOKEN) {
			throw new HttpErrors.Unauthorized('Invalid token');
		}

		const orgs = await this.organizationKeysRepository.find({
			where: {
				key: {eq: SENDLANE_INTEGRATION_KEY},
				secretKeyId: DEFAULT_SECRET_KEY_NAME
			}
		});

		for (const org of orgs) {
			try {
				this.syncSendlane(body, org.organizationId!, true).catch(e => console.error(e));
			} catch (e) {
				console.error(e)
			}
		}
	}

	@post('/sync-sendlane-data/{orgId}')
	@skipGuardCheck()
	async syncSendlane(
		@requestBody({
			content: {
				'application/json': {
					schema: {},
				},
			},
		}) body: any,
		@param.path.number('orgId') orgId: number,
		ignoreNoIntegration: boolean = false
	) {
		if (body.token != SENDLANE_SYNC_TOKEN) {
			throw new HttpErrors.Unauthorized('Invalid token');
		}

		const orgIntegrationKey = await this.organizationKeysRepository.findOne({
			where: {
				organizationId: orgId,
				key: {eq: SENDLANE_INTEGRATION_KEY},
				secretKeyId: DEFAULT_SECRET_KEY_NAME
			}
		})

		const orgCustomIntegrationKey = await this.organizationKeysRepository.findOne({
			where: {
				organizationId: orgId,
				key: {eq: SENDLANE_CUSTOM_INTEGRATION_KEY},
				secretKeyId: DEFAULT_SECRET_KEY_NAME
			}
		})

		const decryptedKey = await this.organizationKeysController.findKey('Sendlane-API-Key', orgId);
		const decryptedToken = await this.organizationKeysController.findKey('Sendlane-Custom-Integration-Token', orgId);

		if (!decryptedKey?.length || !decryptedToken?.length) {
			if (ignoreNoIntegration) {
				return;
			}

			throw new Error('Sendlane API key / token not found');
		}

		const organizationSegments = await this.organizationSegmentRepository.find({
			where: {orgId},
			include: [{
				relation: 'organizationSegmentDetails',
				scope: {
					include: [{relation: 'metricSegment'}],
				},
			}],
		});

		const allSegmentData = await Promise.all(organizationSegments.map(async (segment) => {
			return this.orgSegmentController.getSegmentById(segment.id!, 1, orgId, 0, true, undefined, false);
			// let segmentData: any;
			// let segmentPage;
			// let firstPage = true;
			// do {
			// 	segmentPage = await this.orgSegmentController.getSegmentById(segment.id!, 1, orgId, 250, firstPage, segmentData?.customers);
			// 	segmentData = segmentData || segmentPage;
			// 	segmentData.shoppers = segmentData.shoppers.concat(segmentPage.shoppers);
			// 	firstPage = false;
			// } while (segmentPage?.pagination?.nextPage)

			// return {segment, segmentData};
		}));

		const allCustomers = (await this.raleonUserIdentityRepository.find({
			where: {
				orgId
			}
		})).map(x => ({...x, shopper: undefined as any}));

		for (const segment of allSegmentData) {
			const shopperIds = segment.customers.map((x: any) => x.identityValue);
			segment.shoppers = allCustomers.filter(x => shopperIds.includes(x.identityValue)).map(x => x.shopper);
		}


		const customerIds = allCustomers.map(x => x.identityValue);

		let lastPage;
		let page = 1;
		do {
			lastPage = await this.shopperInfo.getShopperInfo(orgId, page++, 10, undefined, customerIds);
			const shoppers = lastPage.shoppers;
			for (const shopper of shoppers) {
				const match = allCustomers.find(x => shopper.shopify_id == x.identityValue);

				if (match) {
					match.shopper = shopper;
				}
			}
		} while (lastPage?.pagination?.nextPage);



        // get Raleon contact list(s)
		const lists = await this.getSendlaneLists(decryptedKey[0]?.value);
		const raleonList = lists.find((list: any) => list.name === 'Raleon');
		if (!raleonList) {
			await this.createSendlaneContactList(decryptedKey[0]?.value);
		}

		const customIntegration = await this.createSendlaneCustomIntegration(decryptedKey[0]?.value);

		// get fieldmappings for Sendlane
		const integration = await this.integrationRepository.findById(7);
		const fieldMappings = JSON.parse(integration.fieldMappings || '{}');


        // create list of contact signal-related attributes
        // the attributes are the keys, and the list names are the values of the dict stored on fieldmappings
		const fieldNames = Object.values(fieldMappings) as Array<string>;
		const existingFields = await this.getSendlaneCustomFields(decryptedKey[0]?.value);
		const existingFieldNames = existingFields.map((field: any) => field.name) as Array<string>;
		const missingFields = fieldNames.filter((fieldName: string) => !existingFieldNames.includes(fieldName));


        // sync list of attributes to Sendlane custom fields
		for (const fieldName of missingFields) {
			await this.createSendlaneCustomField(decryptedKey[0]?.value, fieldName, 'string');
		}
		const allFields = await this.getSendlaneCustomFields(decryptedKey[0]?.value);


        // sync missing customers as Sendlane contacts
		const allContacts = await this.getAllSendlaneContacts(decryptedKey[0]?.value);
		for (const customer of allCustomers) {
			if (!customer?.shopper?.email) {
				continue;
			}

			// const sendlaneContact = await this.getOrFindSendlaneContact(decryptedKey[0]?.value, contact?.shopper?.email);
			let sendlaneContact = allContacts.find((x: any) => x?.email === customer?.shopper?.email);
			if (!sendlaneContact) {
				sendlaneContact = await this.createSendlaneListContact(decryptedKey[0]?.value, raleonList.id, customer?.shopper?.email);
				allContacts.push(sendlaneContact);
			}
		}



        // sync segments as Sendlane lists
		// const segmentLists = [];
		for (const segment of allSegmentData) {
			let segmentList = lists.find((list: any) => list.name === `Raleon Segment - ${segment.name}`);
			if (!segmentList) {
				segmentList = await this.createSendlaneContactList(decryptedKey[0]?.value, `Raleon Segment - ${segment.name}`);
			}

			// segmentLists.push(segmentList);
			// sync customers
			const listContacts = await this.getAllSendlaneListContacts(decryptedKey[0]?.value, segmentList.id);
			const contactsThatShouldBeRemoved = listContacts.filter((x: any) => !segment.shoppers.find((y: any) => y.email === x.email));
			const contactsThatShouldBeAdded = segment.shoppers.filter((x: any) => !listContacts.find((y: any) => y.email === x.email));

			for (const contact of contactsThatShouldBeRemoved) {
				await this.deleteSendlaneListContact(decryptedKey[0]?.value, segmentList.id, contact.id);
			}

			for (const contact of contactsThatShouldBeAdded) {
				await this.createSendlaneListContact(decryptedKey[0]?.value, segmentList.id, contact.email);
			}
		}

		// sync custom props
		for (const customer of allCustomers) {
			if (!customer?.shopper?.email) {
				continue;
			}

			// const sendlaneContact = await this.getOrFindSendlaneContact(decryptedKey[0]?.value, contact?.shopper?.email);
			let sendlaneContact = allContacts.find((x: any) => x?.email === customer?.shopper?.email);
			if (!sendlaneContact) {
				continue;
			}

            // update custom fields for existing contacts
			const fieldToValueMappings = Object.entries(fieldMappings).map((value, key) => ({
				id: allFields.find((x: any) => x.name === value),
				value: (customer as any)[key]
			}));
			this.updateSendlaneContactCustomFields(decryptedKey[0]?.value, sendlaneContact.id, fieldToValueMappings);
		}
	}

	// private async validateSendlaneApiKey(key: string) {
	// 	if (!key) {
	// 		throw new Error('Invalid Sendlane API key');
	// 	}

	// 	// const url = 'https://api.sendlane.com/v2/contacts';
	// 	// const options = {
	// 	// 	method: 'GET',
	// 	// 	headers: {
	// 	// 		'Content-Type': 'application/json',
	// 	// 		Authorization: `Bearer ${key}`
	// 	// 	}
	// 	// };

	// 	// const data = await fetch(url, options);

	// 	// if (data.status !== 200 || !data.ok) {
	// 	// 	throw new HttpErrors.Unauthorized('Invalid Sendlane API key');
	// 	// }
	// 	// return true;

	// 	return
	// }

	private async getExistingSendlaneCustomIntegration(key: string) {
		if (!key) {
			throw new Error('Invalid Sendlane API key');
		}

		const url = 'https://api.sendlane.com/v2/integrations/custom';
		const options = {
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${key}`
			}
		};

		const response = await fetchWith429Retry(url, options);

		if (response.status < 200 || response.status > 299 || !response.ok) {
			throw new Error(`Sendlane API Error: ${response.status} - ${response.statusText}`);
		}

		const data = await response.json();
		const integrations = data.data;

		return integrations.find((integration: any) => integration.name === 'Raleon');
	}

	private async deleteExistingSendlaneCustomIntegration(key: string, token: string) {
		if (!key) {
			throw new Error('Invalid Sendlane API key');
		}

		if (!(await this.getExistingSendlaneCustomIntegration(key))) {
			return;
		}

		const url = `https://api.sendlane.com/v2/integrations/custom/${token}`;
		const options = {
			method: 'DELETE',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${key}`
			}
		};

		const response = await fetchWith429Retry(url, options);

		if (response.status < 200 || response.status > 299 || !response.ok) {
			throw new Error(`Sendlane API Error: ${response.status} - ${response.statusText}`);
		}

		// const integrations = data.data;

		// return integrations.find((integration: any) => integration.name === 'Raleon');
	}

	private async createSendlaneCustomIntegration(key: string) {
		if (!key) {
			throw new Error('Invalid Sendlane API key');
		}

		const existing = await this.getExistingSendlaneCustomIntegration(key);
		if (existing) {
			return existing;
		}

		const url = 'https://api.sendlane.com/v2/integrations/custom';
		const options = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${key}`
			},
			body: JSON.stringify({
				name: 'Raleon',
				url: 'https://app.raleon.io',
				description: 'Raleon custom integration',
				default_sync_list: 0,
				emailProfile: "Billing"
			})
		};

		const response = await fetchWith429Retry(url, options);

		if (response.status < 200 || response.status > 299 || !response.ok) {
			throw new Error(`Sendlane API Error: ${response.status} - ${response.statusText}`);
		}

		const data = await response.json();


		const testEvent = await this.createSendlaneTestEvent(key, data.data.token, );
		await this.sendSendlaneTestEvent(testEvent, key, data.data.token);

		return data.data;
	}

	private async createSendlaneTestEvent(key: string, token: string) {
		const existingEvents = await this.getSendlaneEvents(key, token);
		if (existingEvents?.some?.((event: any) => event.name === 'Raleon Test')) {
			return existingEvents.find((event: any) => event.name === 'Raleon Test');
		}

		const url = `https://api.sendlane.com/v2/integrations/custom/${token}/events`;
		const options = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${key}`
			},
			body: JSON.stringify({
				name: 'Raleon Test'
			})
		};

		const response = await fetchWith429Retry(url, options);

		if (response.status < 200 || response.status > 299 || !response.ok) {
			throw new Error(`Sendlane API Error: ${response.status} - ${response.statusText}`);
		}

		const data = await response.json();

		return data.data;
	}

	private async getSendlaneEvents(key: string, token: string) {
		const url = `https://api.sendlane.com/v2/integrations/custom/${token}/events`;
		const options = {
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${key}`
			}
		};

		const response = await fetchWith429Retry(url, options);

		if (response.status < 200 || response.status > 299 || !response.ok) {
			throw new Error(`Sendlane API Error: ${response.status} - ${response.statusText}`);
		}

		const data = await response.json();

		return data.data;
	}

	private async sendSendlaneTestEvent(event: any, key: string, token: string) {
		const url = `https://api.sendlane.com/v2/tracking/event`;
		const options = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${key}`
			},
			body: JSON.stringify({
				token,
				custom_event: 'Raleon Test',
				data: {
					test: 'test'
				}
			})
		};

		const response = await fetchWith429Retry(url, options);

		if (response.status < 200 || response.status > 299 || !response.ok) {
			throw new Error(`Sendlane API Error: ${response.status} - ${response.statusText}`);
		}

		const data = await response.json();

		return data.data;
	}

	// private async getSendlaneLists(key: string) {
	// 	const url = `https://api.sendlane.com/v2/lists`;
	// 	const options = {
	// 		method: 'GET',
	// 		headers: {
	// 			'Content-Type': 'application/json',
	// 			Authorization: `Bearer ${key}`
	// 		}
	// 	};

	// 	const response = await fetchWith429Retry(url, options);

	// 	if (response.status !== 200 || !response.ok) {
	// 		throw new HttpErrors.Unauthorized('Invalid Sendlane API key');
	// 	}

	// 	const data = await response.json();

	// 	return data.data;
	// }


	private async deleteSendlaneContactList(key: string, listId: number) {
		const url = `https://api.sendlane.com/v2/lists/${listId}`;
		const options = {
			method: 'DELETE',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${key}`
			}
		};

		const response = await fetchWith429Retry(url, options);

		if (response.status < 200 || response.status > 299 || !response.ok) {
			throw new Error(`Sendlane API Error: ${response.status} - ${response.statusText}`);
		}

		const data = await response.json();

		return data.data;
	}

	private async createSendlaneContactList(key: string, listName = 'Raleon') {
		const url = `https://api.sendlane.com/v2/lists`;
		const options = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${key}`
			},
			body: JSON.stringify({
				name: listName,
				sender_id: 1
			})
		};

		const response = await fetchWith429Retry(url, options);

		if (response.status < 200 || response.status > 299 || !response.ok) {
			throw new Error(`Sendlane API Error: ${response.status} - ${response.statusText}`);
		}

		const data = await response.json();

		return data.data;
	}


	private async getSendlaneLists(key: string) {
		const url = `https://api.sendlane.com/v2/lists`;
		const options = {
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${key}`
			}
		};

		const response = await fetchWith429Retry(url, options);

		if (response.status < 200 || response.status > 299 || !response.ok) {
			throw new Error(`Sendlane API Error: ${response.status} - ${response.statusText}`);
		}

		const data = await response.json();

		return data.data;
	}

	private async getSendlaneCustomFields(key: string) {
		const url = `https://api.sendlane.com/v2/custom-fields`;
		const options = {
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${key}`
			}
		};

		const response = await fetchWith429Retry(url, options);

		if (response.status < 200 || response.status > 299 || !response.ok) {
			throw new Error(`Sendlane API Error: ${response.status} - ${response.statusText}`);
		}

		const data = await response.json();

		return data.data;
	}


	private async createSendlaneCustomField(key: string, name: string, type: 'string') {
		const url = `https://api.sendlane.com/v2/custom-fields`;
		const options = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${key}`
			},
			body: JSON.stringify({
				name,
				// type
			})
		};

		const response = await fetchWith429Retry(url, options);

		if (response.status < 200 || response.status > 299 || !response.ok) {
			throw new Error(`Sendlane API Error: ${response.status} - ${response.statusText}`);
		}

		const data = await response.json();

		return data.data;
	}



	private async createSendlaneListContact(key: string, listId: number, email: string) {
		const url = `https://api.sendlane.com/v2/lists/${listId}/contacts`;
		const options = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${key}`
			},
			body: JSON.stringify({ contacts: [{
				email,
				// "first_name": "string",
				// "last_name": "string",
				// "phone": "+19105555555",
				// email_consent: false,
				// sms_consent: [],
				// tag_ids: [],
				// tagNames: [],
				// custom_fields: []
			}]})
		};

		const response = await fetchWith429Retry(url, options);

		if (response.status < 200 || response.status > 299 || !response.ok) {
			throw new Error(`Sendlane API Error: ${response.status} - ${response.statusText}`);
		}

		const data = await response.json();

		return data.data;
	}


	private async deleteSendlaneListContact(key: string, listId: number, contactId: number) {
		const url = `https://api.sendlane.com/v2/lists/${listId}/contacts/${contactId}`;
		const options = {
			method: 'DELETE',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${key}`
			},
		};

		const response = await fetchWith429Retry(url, options);

		if (response.status < 200 || response.status > 299 || !response.ok) {
			throw new Error(`Sendlane API Error: ${response.status} - ${response.statusText}`);
		}

		const data = await response.json();

		return data.data;
	}



	private async updateSendlaneContactCustomFields(key: string, contactId: number, customFields: Array<{ id: number, value: string }>) {
		const url = `https://api.sendlane.com/v2/contacts/${contactId}/custom-fields`;
		const options = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${key}`
			},
			body: JSON.stringify({ custom_fields: customFields })
		};

		const response = await fetchWith429Retry(url, options);

		if (response.status < 200 || response.status > 299 || !response.ok) {
			throw new Error(`Sendlane API Error: ${response.status} - ${response.statusText}`);
		}

		const data = await response.json();

		return data.data;
	}


	private async getAllSendlaneContacts(key: string) {
		const contacts = [];
		let lastPage: any;
		do {
			lastPage = await this.getSendlaneContacts(key, lastPage?.pagination?.nextPage);
			contacts.push(...lastPage.data);
		} while (lastPage?.pagination?.nextPage);

		return contacts;
	}

	private async getSendlaneContacts(key: string, endpoint = '/v2/contacts') {
		const url = `https://api.sendlane.com${endpoint}`;
		const options = {
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${key}`
			},
			// body: JSON.stringify({ email })
		};

		const response = await fetchWith429Retry(url, options);

		// if (response.status === 422 || response.status === 404) {
		// 	return undefined;
		// }

		if (response.status < 200 || response.status > 299 || !response.ok) {
			throw new Error(`Sendlane API Error: ${response.status} - ${response.statusText}`);
		}

		const data = await response.json();

		return data;
	}


	private async getSendlaneContactByEmail(key: string, email: string) {
		const url = `https://api.sendlane.com/v2/contacts/search?email=${email}`;
		const options = {
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${key}`
			},
			// body: JSON.stringify({ email })
		};

		const response = await fetchWith429Retry(url, options);

		if (response.status === 422 || response.status === 404) {
			return undefined;
		}

		if (response.status < 200 || response.status > 299 || !response.ok) {
			throw new Error(`Sendlane API Error: ${response.status} - ${response.statusText}`);
		}

		const data = await response.json();

		return data.data;
	}

	private async getAllSendlaneListContacts(key: string, listId: number) {
		const contacts = [];
		let lastPage;
		do {
			lastPage = await this.getSendlaneListContacts(key, listId);
			contacts.push(...lastPage.data);
		} while (lastPage?.pagination?.nextPage);

		return contacts
	}

	private async getSendlaneListContacts(key: string, listId: number) {
		const url = `https://api.sendlane.com/v2/lists/${listId}/contacts`;
		const options = {
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${key}`
			}
		};

		const response = await fetchWith429Retry(url, options);

		if (response.status < 200 || response.status > 299 || !response.ok) {
			throw new Error(`Sendlane API Error: ${response.status} - ${response.statusText}`);
		}

		const data = await response.json();

		return data;
	}

	private async updateAndGetSendlaneCustomFieldTypes(apiKey: string, customFieldNames: Array<string>) {
		const existingCustomFields = await this.getSendlaneCustomFields(apiKey);
		for (const customFieldName of customFieldNames) {
			let matchingCustomField = existingCustomFields.find((field: any) => field.name === customFieldName);
			if (!matchingCustomField) {
				matchingCustomField = await this.createSendlaneCustomField(apiKey, customFieldName, 'string');

				existingCustomFields.push(matchingCustomField);
			}
		}

		return existingCustomFields;
	}

	private async getOrFindSendlaneContact(apiKey: string, email: string, customFields: { [name: string]: string } = {}, existingCustomFields?: Array<{id: number, name: string}>) {
		const existingLists = await this.getSendlaneLists(apiKey);
		const raleonList = existingLists.find((list: any) => list.name === 'Raleon');
		if (raleonList) {
			let contact = await this.getSendlaneContactByEmail(apiKey, email);
			if (!contact) {
				contact = await this.createSendlaneListContact(apiKey, raleonList.id, email);
			}

			existingCustomFields = (existingCustomFields || await this.getSendlaneCustomFields(apiKey));

			const fieldsToUpdate = [];
			for (const customFieldName in customFields) {
				let matchingCustomField = existingCustomFields!.find((field: any) => field.name === customFieldName);
				if (!matchingCustomField) {
					matchingCustomField = await this.createSendlaneCustomField(apiKey, customFieldName, 'string');
				}

				fieldsToUpdate.push({ id: matchingCustomField!.id, value: customFields[customFieldName] });
			}

			if (!fieldsToUpdate.length) {
				return
			}

			await this.updateSendlaneContactCustomFields(apiKey, contact.id, fieldsToUpdate);

			return contact;
		}

	}
}

function encrypt(text: string) {
    let iv = crypto.randomBytes(IV_LENGTH);
    let cipher = crypto.createCipheriv('aes-256-cbc', Buffer.from(ENCRYPTION_KEY), iv);
    let encrypted = cipher.update(text);

    encrypted = Buffer.concat([encrypted, cipher.final()]);

    return iv.toString('hex') + ':' + encrypted.toString('hex');
}

function decrypt(text: string) {
    let textParts = text.split(':');
	if (textParts.length === 0) {
        throw new Error('Invalid encrypted text');
    }
    let iv = Buffer.from(textParts.shift()!, 'hex');
    let encryptedText = Buffer.from(textParts.join(':'), 'hex');
    let decipher = crypto.createDecipheriv('aes-256-cbc', Buffer.from(ENCRYPTION_KEY), iv);
    let decrypted = decipher.update(encryptedText);

    decrypted = Buffer.concat([decrypted, decipher.final()]);

    return decrypted.toString();
}

async function fetchWith429Retry(url: string, options: any) {
	let response = await fetch(url, options);
	while (response.status === 429) {
		const retryAfter = response.headers.get('Retry-After') || 10;
		await new Promise(resolve => setTimeout(resolve, retryAfter ? parseInt(retryAfter) * 1000 : 1000));
		response = await fetch(url, options);
	}

	return response;
}

export const KLAVIYO_INTEGRATION_KEY = 'Klaviyo-API-Key';
export const SENDLANE_INTEGRATION_KEY = 'Sendlane-API-Key';
const SENDLANE_CUSTOM_INTEGRATION_KEY = 'Sendlane-Custom-Integration-Token';
